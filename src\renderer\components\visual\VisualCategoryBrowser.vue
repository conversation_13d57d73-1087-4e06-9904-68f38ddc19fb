<template>
  <div class="visual-category-browser">
    <!-- Header with Search and View Controls -->
    <div class="browser-header">
      <CategorySearch
        v-model="searchQuery"
        @search="handleSearch"
        @clear="clearSearch"
      />

      <CategoryFilter
        v-model:view-mode="viewMode"
        v-model:sort-by="sortBy"
        v-model:sort-order="sortOrder"
        @sort-change="handleSortChange"
      />
    </div>

    <!-- Main Content Area -->
    <div class="browser-content">
      <!-- Sidebar Navigation -->
      <CategorySidebar
        v-model:selected-category="selectedCategory"
        v-model:expanded="sidebarExpanded"
        :categories="categories"
        @category-select="handleCategorySelect"
        @toggle="handleSidebarToggle"
      />

      <!-- Thumbnail Grid/List -->
      <CategoryGrid
        :mods="paginatedMods"
        :view-mode="viewMode"
        :loading="loading"
        :current-page="currentPage"
        :total-pages="totalPages"
        @mod-select="selectMod"
        @page-change="goToPage"
        @thumbnail-error="handleThumbnailError"
      />
    </div>

    <!-- Mod Detail Modal -->
    <ModDetailModal
      v-if="selectedMod"
      :mod="selectedMod"
      @close="selectedMod = null"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import {
  SparklesIcon,
  HomeIcon,
  UserIcon,
  CogIcon,
  PuzzlePieceIcon
} from '@heroicons/vue/24/outline';

import CategorySearch from './category/CategorySearch.vue';
import CategoryFilter from './category/CategoryFilter.vue';
import CategorySidebar from './category/CategorySidebar.vue';
import CategoryGrid from './category/CategoryGrid.vue';
import ModDetailModal from './ModDetailModal.vue';
import type { ModData } from '../../../types/ModData';

// Props
interface Props {
  mods: ModData[];
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// Reactive state
const searchQuery = ref('');
const viewMode = ref<'grid' | 'list'>('grid');
const sortBy = ref('name');
const sortOrder = ref<'asc' | 'desc'>('asc');
const selectedCategory = ref('all');
const sidebarExpanded = ref(true);
const selectedMod = ref<ModData | null>(null);
const currentPage = ref(1);
const itemsPerPage = ref(24);

// Categories configuration
const categories = ref([
  { id: 'all', name: 'All Mods', icon: HomeIcon, count: 0 },
  { id: 'cas', name: 'Create-a-Sim', icon: UserIcon, count: 0 },
  { id: 'build-buy', name: 'Build/Buy', icon: HomeIcon, count: 0 },
  { id: 'gameplay', name: 'Gameplay', icon: PuzzlePieceIcon, count: 0 },
  { id: 'scripts', name: 'Script Mods', icon: CogIcon, count: 0 },
  { id: 'other', name: 'Other', icon: SparklesIcon, count: 0 }
]);

// Computed properties
const filteredMods = computed(() => {
  let filtered = props.mods;

  // Apply category filter
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(mod => {
      const category = getCategoryForMod(mod);
      return category === selectedCategory.value;
    });
  }

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(mod =>
      mod.fileName.toLowerCase().includes(query) ||
      mod.category?.toLowerCase().includes(query) ||
      mod.description?.toLowerCase().includes(query)
    );
  }

  // Apply sorting
  filtered.sort((a, b) => {
    let aValue: any, bValue: any;
    
    switch (sortBy.value) {
      case 'name':
        aValue = a.fileName.toLowerCase();
        bValue = b.fileName.toLowerCase();
        break;
      case 'category':
        aValue = a.category || '';
        bValue = b.category || '';
        break;
      case 'size':
        aValue = a.fileSize || 0;
        bValue = b.fileSize || 0;
        break;
      case 'date':
        aValue = a.lastModified || 0;
        bValue = b.lastModified || 0;
        break;
      default:
        return 0;
    }

    if (aValue < bValue) return sortOrder.value === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder.value === 'asc' ? 1 : -1;
    return 0;
  });

  return filtered;
});

const paginatedMods = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredMods.value.slice(start, end);
});

const totalPages = computed(() => {
  return Math.ceil(filteredMods.value.length / itemsPerPage.value);
});



// Methods
const handleSearch = () => {
  currentPage.value = 1; // Reset to first page when searching
};

const clearSearch = () => {
  currentPage.value = 1;
};

const handleSortChange = () => {
  currentPage.value = 1; // Reset to first page when sorting changes
};

const handleCategorySelect = (categoryId: string) => {
  currentPage.value = 1;
};

const handleSidebarToggle = () => {
  // Sidebar toggle is handled by v-model in CategorySidebar
};

const selectMod = (mod: ModData) => {
  selectedMod.value = mod;
};

const goToPage = (page: number) => {
  currentPage.value = page;
  // Scroll to top of mod grid
  const modGrid = document.querySelector('.mod-display-area');
  if (modGrid) {
    modGrid.scrollTop = 0;
  }
};

const handleThumbnailError = (modId: string) => {
  console.warn(`Thumbnail failed to load for mod: ${modId}`);
  // Could implement fallback thumbnail logic here
};

const getCategoryForMod = (mod: ModData): string => {
  // Map mod categories to our category system
  const category = mod.category?.toLowerCase() || '';
  
  if (category.includes('cas') || category.includes('create-a-sim')) return 'cas';
  if (category.includes('build') || category.includes('buy') || category.includes('object')) return 'build-buy';
  if (category.includes('script') || category.includes('python')) return 'scripts';
  if (category.includes('gameplay') || category.includes('trait') || category.includes('career')) return 'gameplay';
  
  return 'other';
};

const updateCategoryCounts = () => {
  categories.value.forEach(category => {
    if (category.id === 'all') {
      category.count = props.mods.length;
    } else {
      category.count = props.mods.filter(mod => getCategoryForMod(mod) === category.id).length;
    }
  });
};

// Watchers
watch(() => props.mods, updateCategoryCounts, { immediate: true });

// Lifecycle
onMounted(() => {
  updateCategoryCounts();
});
</script>

<style scoped>
.visual-category-browser {
  @apply flex flex-col h-full bg-gray-50 dark:bg-gray-900;
}

/* Header Styles */
.browser-header {
  @apply flex items-center justify-between p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700;
}



/* Content Area */
.browser-content {
  @apply flex flex-1 overflow-hidden;
}




</style>
