<template>
  <div class="category-sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
      <h3 class="sidebar-title">Categories</h3>
      <button 
        @click="handleToggleSidebar" 
        class="sidebar-toggle"
        :title="expanded ? 'Collapse sidebar' : 'Expand sidebar'"
      >
        <ChevronLeftIcon v-if="expanded" class="w-4 h-4" />
        <ChevronRightIcon v-else class="w-4 h-4" />
      </button>
    </div>
    
    <!-- Category List -->
    <div v-if="expanded" class="category-list">
      <div
        v-for="category in categories"
        :key="category.id"
        :class="['category-item', { active: selectedCategory === category.id }]"
        @click="handleCategorySelect(category.id)"
        @keydown.enter="handleCategorySelect(category.id)"
        @keydown.space.prevent="handleCategorySelect(category.id)"
        tabindex="0"
        :aria-label="`${category.name} category, ${category.count} mods`"
        role="button"
      >
        <div class="category-icon">
          <component :is="category.icon" class="w-5 h-5" />
        </div>
        <div class="category-info">
          <span class="category-name">{{ category.name }}</span>
          <span class="category-count">{{ category.count }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/24/outline';
import type { Component } from 'vue';

// Types
interface Category {
  id: string;
  name: string;
  icon: Component;
  count: number;
}

// Props
interface Props {
  categories: Category[];
  selectedCategory: string;
  expanded: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:selectedCategory': [categoryId: string];
  'update:expanded': [expanded: boolean];
  categorySelect: [categoryId: string];
  toggle: [expanded: boolean];
}>();

// Methods
const handleCategorySelect = (categoryId: string) => {
  emit('update:selectedCategory', categoryId);
  emit('categorySelect', categoryId);
};

const handleToggleSidebar = () => {
  const newExpanded = !props.expanded;
  emit('update:expanded', newExpanded);
  emit('toggle', newExpanded);
};
</script>

<style scoped>
.category-sidebar {
  @apply bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 
         transition-all duration-300 ease-in-out;
  width: 280px;
}

.sidebar-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700;
}

.sidebar-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.sidebar-toggle {
  @apply p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200
         transition-colors duration-200 rounded
         focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50;
}

.category-list {
  @apply p-2 space-y-1;
}

.category-item {
  @apply flex items-center p-3 rounded-lg cursor-pointer
         hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200
         focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50;
}

.category-item.active {
  @apply bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200;
}

.category-icon {
  @apply mr-3 text-gray-500 dark:text-gray-400 transition-colors duration-200;
}

.category-item.active .category-icon {
  @apply text-green-600 dark:text-green-400;
}

.category-info {
  @apply flex-1 flex items-center justify-between;
}

.category-name {
  @apply font-medium;
}

.category-count {
  @apply text-sm text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-600 
         px-2 py-1 rounded-full transition-colors duration-200;
}

.category-item.active .category-count {
  @apply bg-green-200 dark:bg-green-800 text-green-800 dark:text-green-200;
}

/* Accessibility improvements */
.category-item:focus {
  @apply ring-2 ring-green-500 ring-opacity-50;
}

@media (prefers-reduced-motion: reduce) {
  .category-sidebar,
  .category-item,
  .sidebar-toggle,
  .category-icon,
  .category-count {
    @apply transition-none;
  }
}
</style>
