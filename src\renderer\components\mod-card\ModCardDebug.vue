<template>
  <div v-if="showDebugInfo" class="mod-card-debug">
    <!-- Raw Data (Debug Mode) -->
    <div class="mod-card__section mod-card__section--debug">
      <h4 class="mod-card__section-title">
        <CodeBracketIcon class="mod-card__section-icon" />
        Raw Analysis Data
      </h4>
      <pre class="mod-card__debug-data">{{ JSON.stringify(modData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CodeBracketIcon } from '@heroicons/vue/24/outline';

// Types
interface ModData {
  fileName: string;
  fileExtension: string;
  fileSize: number;
  author?: string;
  version?: string;
  metadataConfidence?: number;
  [key: string]: any; // Allow any additional properties for debug display
}

// Props
interface Props {
  modData: ModData;
  showDebugInfo: boolean;
}

const props = defineProps<Props>();
</script>

<style scoped>
.mod-card-debug {
  display: flex;
  flex-direction: column;
}

.mod-card__section {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
}

.mod-card__section:last-child {
  border-bottom: none;
}

.mod-card__section--debug {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  margin: var(--space-4);
}

.mod-card__section-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
}

.mod-card__section-icon {
  width: 20px;
  height: 20px;
  color: var(--sims-blue);
  flex-shrink: 0;
}

.mod-card__debug-data {
  background-color: var(--bg-code);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  padding: var(--space-4);
  font-family: var(--font-mono);
  font-size: var(--text-xs);
  line-height: var(--leading-relaxed);
  color: var(--text-code);
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 400px;
  overflow-y: auto;
}

/* Scrollbar styling for debug data */
.mod-card__debug-data::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.mod-card__debug-data::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
}

.mod-card__debug-data::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--radius-sm);
}

.mod-card__debug-data::-webkit-scrollbar-thumb:hover {
  background: var(--border-strong);
}
</style>
