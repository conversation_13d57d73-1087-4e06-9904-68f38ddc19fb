<template>
  <div class="variation-indicator" :title="`${variationCount} color variations available`">
    <span class="variation-count">{{ variationCount }}</span>
    <span class="variation-label">swatches</span>
  </div>
</template>

<script setup lang="ts">
// Props
interface Props {
  variationCount: number;
}

const props = defineProps<Props>();
</script>

<style scoped>
/* Premium variation indicator - positioned in overlay */
.variation-indicator {
  @apply text-xs font-bold px-3 py-1.5 rounded-full transition-all duration-300 ease-out;
  background: var(--primary);
  color: white;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4), 0 1px 3px rgba(0, 0, 0, 0.6);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.variation-count {
  font-weight: 900;
}

.variation-label {
  margin-left: 0.25rem;
}
</style>
