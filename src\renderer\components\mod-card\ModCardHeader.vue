<template>
  <div class="mod-card__header" @click="handleHeaderClick">
    <div class="mod-card__header-main">
      <div class="mod-card__title-section">
        <h3 class="mod-card__title">
          {{ getActualModName() }}
        </h3>
        <div class="mod-card__metadata">
          <span class="mod-card__author" v-if="modData?.author">
            by {{ modData.author }}
          </span>
          <span class="mod-card__version" v-if="modData?.version">
            v{{ modData.version }}
          </span>
          <span class="mod-card__confidence" v-if="modData?.metadataConfidence">
            {{ modData.metadataConfidence }}% confidence
          </span>
          <span v-if="modData?.hasStringTable" class="mod-card__stringtable-indicator" title="Mod name extracted from StringTable">
            📋 STBL
          </span>
        </div>
      </div>
      
      <div class="mod-card__quick-stats">
        <div class="mod-card__file-info">
          <span class="mod-card__file-size">{{ formatFileSize(modData?.fileSize || 0) }}</span>
          <span
            class="mod-card__file-type"
            :class="`file-type--${(modData?.fileExtension || '.package').slice(1)}`"
            :title="getFileTypeDescription(modData?.fileExtension || '.package')"
          >
            {{ getFileTypeLabel(modData?.fileExtension || '.package') }}
          </span>
        </div>
      </div>
    </div>
    
    <button class="mod-card__expand-button" :class="{ 'expanded': isExpanded }">
      <ChevronDownIcon class="mod-card__expand-icon" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { ChevronDownIcon } from '@heroicons/vue/24/outline';

// Types
interface ModData {
  fileName: string;
  fileExtension: string;
  fileSize: number;
  author?: string;
  version?: string;
  metadataConfidence?: number;
  actualModName?: string;
  hasStringTable?: boolean;
}

// Props
interface Props {
  modData: ModData;
  isExpanded: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'toggle-expanded': [];
}>();

// Methods
const handleHeaderClick = () => {
  emit('toggle-expanded');
};

const getDisplayName = (fileName: string): string => {
  // Remove file extension and clean up the name for better readability
  let name = fileName.replace(/\.(package|ts4script)$/i, '');

  // Replace underscores with spaces and capitalize words
  name = name.replace(/_/g, ' ');

  // Capitalize each word
  name = name.replace(/\b\w/g, l => l.toUpperCase());

  return name;
};

const getActualModName = (): string => {
  // Use actual mod name from StringTable if available, otherwise fall back to filename
  if (props.modData?.actualModName) {
    return props.modData.actualModName;
  }

  return getDisplayName(props.modData?.fileName || 'Unknown');
};

const getFileTypeLabel = (extension: string): string => {
  switch (extension.toLowerCase()) {
    case '.package':
      return 'Package';
    case '.ts4script':
      return 'Script';
    default:
      return extension;
  }
};

const getFileTypeDescription = (extension: string): string => {
  switch (extension.toLowerCase()) {
    case '.package':
      return 'Sims 4 Package File - Contains game assets like CAS items, objects, lots, or gameplay modifications';
    case '.ts4script':
      return 'Sims 4 Script File - Contains Python code that modifies game behavior and adds new functionality';
    default:
      return `${extension} file`;
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
</script>

<style scoped>
/* Header - Enhanced Apple-inspired design */
.mod-card__header {
  padding: var(--space-5) var(--space-6);
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  transition: all var(--duration-200) var(--ease-out);
  border-bottom: 1px solid transparent;
}

.mod-card__header:hover {
  background-color: var(--bg-secondary);
  border-bottom-color: var(--border-light);
}

.mod-card__header-main {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  width: 100%;
  margin-right: var(--space-4);
}

.mod-card__title-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  min-width: 0;
}

.mod-card__title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  line-height: var(--leading-tight);
  word-break: break-word;
}

.mod-card__metadata {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  align-items: center;
}

.mod-card__author {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  border: 1px solid var(--border-light);
}

.mod-card__version {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  border: 1px solid var(--border-light);
}

.mod-card__confidence {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  background: var(--bg-quaternary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
}

.mod-card__stringtable-indicator {
  font-size: var(--text-xs);
  color: var(--text-accent);
  background: var(--accent-bg);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--accent-border);
  cursor: help;
}

.mod-card__quick-stats {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.mod-card__file-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.mod-card__file-size {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.mod-card__file-type {
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.file-type--package {
  background: var(--sims-blue-bg);
  color: var(--sims-blue);
  border: 1px solid var(--sims-blue-border);
}

.file-type--ts4script {
  background: var(--sims-green-bg);
  color: var(--sims-green);
  border: 1px solid var(--sims-green-border);
}

.mod-card__expand-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--duration-200) var(--ease-out);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.mod-card__expand-button:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.mod-card__expand-button.expanded .mod-card__expand-icon {
  transform: rotate(180deg);
}

.mod-card__expand-icon {
  width: 20px;
  height: 20px;
  transition: transform var(--duration-200) var(--ease-out);
}
</style>
