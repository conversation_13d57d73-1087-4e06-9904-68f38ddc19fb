<template>
  <div class="mod-card-content">
    <!-- Enhanced Object Content Section -->
    <div v-if="hasObjectContent" class="mod-card__section">
      <h4 class="mod-card__section-title">
        <CubeIcon class="mod-card__section-icon" />
        Object Content
      </h4>

      <!-- Universal Subcategory Display for Objects -->
      <UniversalSubcategoryDisplay
        v-if="objectClassificationData"
        :classification-result="objectClassificationData"
      />

      <!-- General Object Content Info -->
      <div v-else class="object-content-info">
        <div class="object-content-summary">
          <span class="object-content-type">{{ getObjectContentType() }}</span>
          <span class="object-content-description">{{ getObjectContentDescription() }}</span>
        </div>
      </div>
    </div>

    <!-- Quality Assessment Section -->
    <div v-if="qualityAssessment" class="mod-card__section">
      <h4 class="mod-card__section-title">
        <StarIcon class="mod-card__section-icon" />
        Quality Assessment
      </h4>
      <QualityAssessmentDisplay :assessment="qualityAssessment" />
    </div>
    
    <!-- Dependencies Section -->
    <div v-if="dependencies" class="mod-card__section">
      <h4 class="mod-card__section-title">
        <LinkIcon class="mod-card__section-icon" />
        Dependencies & Conflicts
      </h4>
      <DependencyDisplay :dependencies="dependencies" />
    </div>
    
    <!-- Performance Impact -->
    <div v-if="performanceData" class="mod-card__section">
      <h4 class="mod-card__section-title">
        <BoltIcon class="mod-card__section-icon" />
        Performance Impact
      </h4>
      <PerformanceDisplay :performance="performanceData" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  CubeIcon,
  StarIcon,
  LinkIcon,
  BoltIcon
} from '@heroicons/vue/24/outline';

import QualityAssessmentDisplay from '../QualityAssessmentDisplay.vue';
import DependencyDisplay from '../DependencyDisplay.vue';
import PerformanceDisplay from '../PerformanceDisplay.vue';
import UniversalSubcategoryDisplay from '../UniversalSubcategoryDisplay.vue';

// Types
interface ModData {
  fileName: string;
  fileExtension: string;
  fileSize: number;
  author?: string;
  version?: string;
  metadataConfidence?: number;
  resourceCount?: number;
  dependencyData?: any;
  qualityAssessmentData?: any;
  processingTime: number;

  // Enhanced Object Content Analysis Fields
  objectContent?: {
    totalItems: number;
    items: Array<{
      category: string;
      subcategory: string;
      roomAssignment: string;
      function: string;
      style: string;
      price: number;
      isDecor: boolean;
      isFunctional: boolean;
      tags: string[];
      description: string;
    }>;
  };

  // Enhanced Classification Data for UI Display
  objectClassification?: {
    category: string;
    subcategories: string[];
    specificType: string;
    confidence: number;
    detectionMethod: string;
    roomTypes: string[];
    functionality: string[];
    tags: string[];
    metadata: {
      priceRange?: string;
      description?: string;
    };
  };

  universalClassification?: {
    category: string;
    subcategories: string[];
    confidence: number;
    detectionMethod: string;
    keywords: string[];
    metadata: any;
    tags: string[];
  };

  resourceIntelligenceData?: {
    resourceBreakdown?: {
      objects: number;
    };
  };
}

// Props
interface Props {
  modData: ModData;
}

const props = defineProps<Props>();

// Computed properties
const hasObjectContent = computed(() => {
  // First check if we have enhanced object classification data
  if (props.modData?.objectClassification) {
    return true;
  }

  // Check if we have object content from content analysis
  if (props.modData?.objectContent?.totalItems > 0) {
    return true;
  }

  // Fallback to filename-based detection
  const fileName = (props.modData?.fileName || '').toLowerCase();
  return fileName.includes('furniture') ||
         fileName.includes('decoration') ||
         fileName.includes('appliance') ||
         fileName.includes('door') ||
         fileName.includes('window') ||
         fileName.includes('wall') ||
         fileName.includes('floor') ||
         fileName.includes('bed') ||
         fileName.includes('chair') ||
         fileName.includes('table') ||
         fileName.includes('desk') ||
         fileName.includes('sofa') ||
         fileName.includes('couch') ||
         fileName.includes('cabinet') ||
         fileName.includes('shelf') ||
         fileName.includes('lamp') ||
         fileName.includes('mirror') ||
         fileName.includes('plant') ||
         fileName.includes('rug') ||
         fileName.includes('curtain') ||
         fileName.includes('painting') ||
         fileName.includes('sculpture') ||
         fileName.includes('vase') ||
         fileName.includes('clock') ||
         fileName.includes('tv') ||
         fileName.includes('computer') ||
         fileName.includes('fridge') ||
         fileName.includes('stove') ||
         fileName.includes('toilet') ||
         fileName.includes('sink') ||
         fileName.includes('shower') ||
         fileName.includes('bathtub') ||
         (props.modData?.resourceIntelligenceData?.resourceBreakdown?.objects > 0);
});

const objectClassificationData = computed(() => {
  // Get universal classification data for object content
  const fileName = (props.modData?.fileName || '').toLowerCase();

  // Check if we have enhanced universal classification data for objects
  if (props.modData?.objectClassification) {
    return props.modData.objectClassification;
  }

  // Generate classification data from available information
  if (hasObjectContent.value) {
    const objectContent = props.modData?.objectContent?.items?.[0];
    if (objectContent) {
      return {
        category: objectContent.category || 'furniture',
        subcategories: [objectContent.subcategory || 'unknown'],
        confidence: 0.7,
        detectionMethod: 'filename',
        keywords: fileName.split(/[\s_-]+/).filter(k => k.length > 2),
        metadata: {},
        tags: objectContent.tags || []
      };
    }
  }

  return null;
});

const qualityAssessment = computed(() => props.modData?.qualityAssessmentData);
const dependencies = computed(() => props.modData?.dependencyData);
const performanceData = computed(() => {
  // Extract performance data from modData if available
  if (props.modData?.processingTime) {
    return {
      processingTime: props.modData.processingTime,
      resourceCount: props.modData.resourceCount || 0,
      fileSize: props.modData.fileSize || 0
    };
  }
  return null;
});

// Methods
const getObjectContentType = (): string => {
  const fileName = (props.modData?.fileName || '').toLowerCase();

  if (fileName.includes('furniture') || fileName.includes('chair') || fileName.includes('sofa') || fileName.includes('table')) return 'Furniture';
  if (fileName.includes('decoration') || fileName.includes('art') || fileName.includes('plant') || fileName.includes('lamp')) return 'Decorations';
  if (fileName.includes('appliance') || fileName.includes('stove') || fileName.includes('fridge') || fileName.includes('tv')) return 'Appliances';
  if (fileName.includes('door') || fileName.includes('window') || fileName.includes('wall') || fileName.includes('floor')) return 'Build Items';

  return 'Object Content';
};

const getObjectContentDescription = (): string => {
  const fileName = (props.modData?.fileName || '').toLowerCase();

  if (fileName.includes('furniture')) {
    return 'Adds new furniture pieces for your Sims\' homes';
  }
  if (fileName.includes('decoration')) {
    return 'Adds new decorative items to enhance your builds';
  }
  if (fileName.includes('appliance')) {
    return 'Adds new functional appliances for your Sims';
  }
  if (fileName.includes('door') || fileName.includes('window')) {
    return 'Adds new architectural elements for building';
  }

  return 'Enhances Build/Buy mode with new objects';
};
</script>

<style scoped>
.mod-card-content {
  display: flex;
  flex-direction: column;
}

.mod-card__section {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
}

.mod-card__section:last-child {
  border-bottom: none;
}

.mod-card__section-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
}

.mod-card__section-icon {
  width: 20px;
  height: 20px;
  color: var(--sims-blue);
  flex-shrink: 0;
}

/* Object Content Styles */
.object-content-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.object-content-summary {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.object-content-type {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  font-size: var(--text-base);
}

.object-content-description {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
}
</style>
