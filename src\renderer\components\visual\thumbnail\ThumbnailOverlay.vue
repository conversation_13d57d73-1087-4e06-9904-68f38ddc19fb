<template>
  <!-- Modern Hover-Activated Text Overlay - Grid View Only -->
  <div v-if="viewMode === 'grid'" class="hover-text-overlay">
    <!-- Top section: Status badges -->
    <div class="overlay-top">
      <ThumbnailBadges 
        :mod="mod"
        :is-high-quality="isHighQuality"
      />

      <!-- Swatch indicator badge (shows if multiple swatches exist) -->
      <ThumbnailVariationIndicator
        v-if="hasMultipleVariations"
        :variation-count="variationCount"
      />
    </div>

    <!-- Bottom section: Title and metadata -->
    <div class="overlay-bottom">
      <h3 class="overlay-title" :title="mod.fileName">{{ displayName }}</h3>
      <div class="overlay-meta">
        <span class="category-badge" :class="getCategoryClass()">
          {{ mod.category || 'Unknown' }}
        </span>
        <span class="file-size">{{ formatFileSize(mod.fileSize) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { ModData } from '../../../../types/ModData';
import ThumbnailBadges from './ThumbnailBadges.vue';
import ThumbnailVariationIndicator from './ThumbnailVariationIndicator.vue';

// Props
interface Props {
  mod: ModData;
  viewMode: 'grid' | 'list';
}

const props = defineProps<Props>();

// Computed properties
const displayName = computed(() => {
  const name = props.mod.fileName;
  // Remove file extension and clean up the name
  return name.replace(/\.(package|ts4script)$/i, '').replace(/_/g, ' ');
});

const isHighQuality = computed(() => {
  // Determine if this is a high-quality mod based on various factors
  const hasHighResources = (props.mod.resourceCount || 0) > 50;
  const hasLargeSize = (props.mod.fileSize || 0) > 1024 * 1024; // > 1MB
  const hasEnhancedAnalysis = props.mod.objectClassification?.confidence > 0.8;
  
  return hasHighResources || hasLargeSize || hasEnhancedAnalysis;
});

const thumbnailVariations = computed(() => {
  const result = props.mod.thumbnailVariations || [];
  console.log(`🎨 [ThumbnailOverlay] ${props.mod.fileName} - variations: ${result.length}`);
  return result;
});

const hasMultipleVariations = computed(() => {
  const result = props.mod.hasMultipleVariations || thumbnailVariations.value.length > 0;
  console.log(`🔢 [ThumbnailOverlay] ${props.mod.fileName} - hasMultipleVariations: ${result}`);
  return result;
});

const variationCount = computed(() => {
  return thumbnailVariations.value.length;
});

// Methods
const getCategoryClass = () => {
  const category = props.mod.category?.toLowerCase() || 'other';
  
  if (category.includes('cas') || category.includes('hair') || category.includes('clothing')) {
    return 'category-cas';
  }
  if (category.includes('build') || category.includes('furniture') || category.includes('decoration')) {
    return 'category-build';
  }
  if (category.includes('script') || category.includes('mod')) {
    return 'category-script';
  }
  if (category.includes('gameplay') || category.includes('trait')) {
    return 'category-gameplay';
  }
  
  return 'category-other';
};

const formatFileSize = (bytes: number): string => {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
</script>

<style scoped>
/* Modern Hover-Activated Text Overlay - Grid View Only */
.hover-text-overlay {
  @apply absolute inset-0 opacity-0 transition-all duration-400 ease-out
         flex flex-col justify-between p-3;
  background: linear-gradient(180deg,
    rgba(0, 0, 0, 0.85) 0%,
    rgba(0, 0, 0, 0.6) 40%,
    rgba(0, 0, 0, 0.9) 100%);
  pointer-events: none; /* Allow clicks to pass through to main card */
  backdrop-filter: blur(12px);
  z-index: 5;
  border-radius: 10px; /* Match card border radius */
}

/* Overlay Top Section - Status badges and indicators */
.overlay-top {
  @apply flex items-start justify-between;
}

/* Overlay Bottom Section - Title and metadata */
.overlay-bottom {
  @apply flex flex-col space-y-2;
}

/* Overlay Title Styling */
.overlay-title {
  @apply text-white font-bold text-sm leading-tight;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8), 0 1px 3px rgba(0, 0, 0, 0.9);

  /* Advanced text overflow handling */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

/* Overlay Metadata Styling */
.overlay-meta {
  @apply flex items-center justify-between gap-2;
}

/* Professional badges with strategic color system - Overlay optimized */
.category-badge {
  @apply px-2 py-1 rounded-full text-xs font-bold;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);

  /* Smooth transitions with professional easing */
  transition: all 0.3s ease-out;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4), 0 1px 3px rgba(0, 0, 0, 0.6);
}

.category-cas {
  /* Strategic purple for CAS content */
  background: var(--premium);
}

.category-build {
  /* Strategic blue for Build/Buy content */
  background: var(--primary);
}

.category-script {
  /* Strategic green for script content */
  background: var(--success);
}

.category-gameplay {
  /* Strategic orange for gameplay content */
  background: var(--warning);
}

.category-other {
  background: var(--secondary);
}

.file-size {
  @apply text-xs font-medium;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}
</style>
