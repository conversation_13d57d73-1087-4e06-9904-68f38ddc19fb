<template>
  <div class="badge-container">
    <span v-if="mod.hasConflicts" class="badge badge-warning" title="Has conflicts">
      <ExclamationTriangleIcon class="w-3 h-3" />
    </span>
    <span v-if="mod.isCorrupted" class="badge badge-error" title="Corrupted file">
      <XCircleIcon class="w-3 h-3" />
    </span>
    <span v-if="isHighQuality" class="badge badge-success" title="High quality">
      <SparklesIcon class="w-3 h-3" />
    </span>
  </div>
</template>

<script setup lang="ts">
import {
  ExclamationTriangleIcon,
  XCircleIcon,
  SparklesIcon
} from '@heroicons/vue/24/outline';

import type { ModData } from '../../../../types/ModData';

// Props
interface Props {
  mod: ModData;
  isHighQuality: boolean;
}

const props = defineProps<Props>();
</script>

<style scoped>
.badge-container {
  @apply flex space-x-2;
}

.badge {
  @apply flex items-center px-2 py-1 rounded-full text-xs font-bold
         transition-all duration-300 ease-out;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4), 0 1px 3px rgba(0, 0, 0, 0.6);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.badge-warning {
  background: var(--warning);
  color: white;
}

.badge-error {
  background: var(--error);
  color: white;
}

.badge-success {
  background: var(--success);
  color: white;
}
</style>
