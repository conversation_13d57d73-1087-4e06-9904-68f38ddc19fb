<template>
  <div class="search-section">
    <div class="search-input-container">
      <MagnifyingGlassIcon class="search-icon" />
      <input
        v-model="searchQuery"
        type="text"
        placeholder="Search mods by name, category, or content..."
        class="search-input"
        @input="handleSearch"
        @keydown.enter="handleSearch"
      />
      <button 
        v-if="searchQuery" 
        @click="clearSearch" 
        class="clear-search"
        title="Clear search"
      >
        <XMarkIcon class="w-4 h-4" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/vue/24/outline';

// Props
interface Props {
  modelValue?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: ''
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string];
  search: [query: string];
  clear: [];
}>();

// Reactive state
const searchQuery = ref(props.modelValue);

// Methods
const handleSearch = () => {
  emit('update:modelValue', searchQuery.value);
  emit('search', searchQuery.value);
};

const clearSearch = () => {
  searchQuery.value = '';
  emit('update:modelValue', '');
  emit('clear');
};

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  searchQuery.value = newValue;
});
</script>

<style scoped>
.search-section {
  @apply flex-1 max-w-md;
}

.search-input-container {
  @apply relative;
}

.search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400;
}

.search-input {
  @apply w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
         bg-white dark:bg-gray-700 text-gray-900 dark:text-white
         focus:ring-2 focus:ring-green-500 focus:border-transparent
         placeholder-gray-500 dark:placeholder-gray-400
         transition-colors duration-200;
}

.search-input:focus {
  @apply outline-none;
}

.clear-search {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 
         text-gray-400 hover:text-gray-600 dark:hover:text-gray-300
         transition-colors duration-200 p-1 rounded
         hover:bg-gray-100 dark:hover:bg-gray-600;
}

.clear-search:focus {
  @apply outline-none ring-2 ring-green-500 ring-opacity-50;
}
</style>
