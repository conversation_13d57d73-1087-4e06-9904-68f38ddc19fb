<template>
  <!-- Quick Actions Menu (positioned absolutely) -->
  <div v-if="showActions" class="quick-actions-container">
    <!-- Action Buttons -->
    <div class="action-buttons">
      <button
        class="action-btn"
        :class="{ active: isFavorite }"
        @click.stop="toggleFavorite"
        :title="isFavorite ? 'Remove from favorites' : 'Add to favorites'"
      >
        <HeartIcon class="w-4 h-4" />
      </button>
      
      <button
        class="action-btn"
        @click.stop="toggleQuickActions"
        title="More actions"
      >
        <EllipsisVerticalIcon class="w-4 h-4" />
      </button>
    </div>

    <!-- Quick Actions Menu -->
    <div v-if="showQuickActions" class="quick-actions-menu">
      <button class="quick-action" @click="openInExplorer">
        <FolderOpenIcon class="w-4 h-4" />
        <span>Open in Explorer</span>
      </button>
      
      <button class="quick-action" @click="copyPath">
        <ClipboardIcon class="w-4 h-4" />
        <span>Copy Path</span>
      </button>
      
      <button class="quick-action" @click="showDetails">
        <InformationCircleIcon class="w-4 h-4" />
        <span>Show Details</span>
      </button>
      
      <hr class="my-1 border-gray-200 dark:border-gray-600" />
      
      <button class="quick-action danger" @click="deleteMod">
        <TrashIcon class="w-4 h-4" />
        <span>Delete</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import {
  HeartIcon,
  EllipsisVerticalIcon,
  FolderOpenIcon,
  ClipboardIcon,
  InformationCircleIcon,
  TrashIcon
} from '@heroicons/vue/24/outline';

import type { ModData } from '../../../../types/ModData';

// Props
interface Props {
  mod: ModData;
  showActions?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showActions: false
});

// Emits
const emit = defineEmits<{
  favorite: [modId: string, isFavorite: boolean];
  delete: [modId: string];
  click: [mod: ModData];
}>();

// Reactive state
const showQuickActions = ref(false);
const isFavorite = ref(false);

// Methods
const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value;
  emit('favorite', props.mod.id, isFavorite.value);
};

const toggleQuickActions = () => {
  showQuickActions.value = !showQuickActions.value;
};

const openInExplorer = () => {
  // Emit event to parent to handle file explorer opening
  window.electronAPI?.openInExplorer?.(props.mod.filePath);
  showQuickActions.value = false;
};

const copyPath = async () => {
  try {
    await navigator.clipboard.writeText(props.mod.filePath);
    // Could show a toast notification here
  } catch (error) {
    console.error('Failed to copy path:', error);
  }
  showQuickActions.value = false;
};

const showDetails = () => {
  emit('click', props.mod);
  showQuickActions.value = false;
};

const deleteMod = () => {
  if (confirm(`Are you sure you want to delete "${props.mod.fileName}"?`)) {
    emit('delete', props.mod.id);
  }
  showQuickActions.value = false;
};

// Close quick actions when clicking outside
const handleClickOutside = (event: Event) => {
  if (!event.target || !(event.target as Element).closest('.quick-actions-menu')) {
    showQuickActions.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.quick-actions-container {
  @apply absolute top-2 right-2 z-10;
}

/* Enhanced action buttons with Apple-inspired micro-interactions */
.action-buttons {
  @apply flex space-x-2 self-end opacity-0 transition-all duration-300 ease-out;
  transform: translateY(4px);
}

.mod-thumbnail-card:hover .action-buttons {
  @apply opacity-100;
  transform: translateY(0);
}

.action-btn {
  @apply p-2 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300
         rounded-full shadow-sm hover:shadow-md transition-all duration-200 ease-out;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
}

.action-btn:hover {
  @apply bg-white dark:bg-gray-700;
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.action-btn.active {
  @apply text-red-500;
  background: rgba(239, 68, 68, 0.1);
}

/* Quick Actions Menu */
.quick-actions-menu {
  @apply absolute top-full right-0 mt-1 bg-white dark:bg-gray-800 
         border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg
         py-1 z-10 min-w-40;
}

.quick-action {
  @apply w-full flex items-center space-x-2 px-3 py-2 text-sm
         text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700
         transition-colors;
}

.quick-action.danger {
  @apply text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900;
}
</style>
