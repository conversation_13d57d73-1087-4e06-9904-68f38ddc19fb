<template>
  <div class="thumbnail-container">
    <div v-if="loading" class="thumbnail-loading">
      <div class="loading-spinner"></div>
    </div>

    <!-- Primary Thumbnail Display -->
    <img
      v-if="thumbnailUrl"
      :src="thumbnailUrl"
      :alt="`${fileName} thumbnail`"
      class="thumbnail-image"
      @load="handleLoad"
      @error="handleError"
    />

    <div v-else class="thumbnail-placeholder">
      <component :is="placeholderIcon" class="placeholder-icon" />
      <span class="placeholder-text">{{ placeholderText }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  DocumentIcon,
  UserIcon,
  HomeIcon,
  CogIcon,
  PuzzlePieceIcon
} from '@heroicons/vue/24/outline';

import type { ModData } from '../../../../types/ModData';

// Props
interface Props {
  mod: ModData;
  viewMode: 'grid' | 'list';
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  load: [];
  error: [modId: string];
}>();

// Reactive state
const loading = ref(false);

// Computed properties
const fileName = computed(() => props.mod.fileName);

const thumbnailUrl = computed(() => {
  // Use enhanced thumbnail data if available (imageData is the correct property)
  if (primaryThumbnail.value?.imageData) {
    console.log(`🎯 [ThumbnailImage] ${props.mod.fileName} - Using primaryThumbnail.imageData`);
    return primaryThumbnail.value.imageData;
  }
  // Fallback to legacy thumbnail data
  if (props.mod.thumbnailData) {
    console.log(`🎯 [ThumbnailImage] ${props.mod.fileName} - Using mod.thumbnailData`);
    return props.mod.thumbnailData;
  }
  if (props.mod.thumbnailUrl) {
    console.log(`🎯 [ThumbnailImage] ${props.mod.fileName} - Using mod.thumbnailUrl`);
    return props.mod.thumbnailUrl;
  }
  console.log(`❌ [ThumbnailImage] ${props.mod.fileName} - No thumbnail data found`);
  return null;
});

const primaryThumbnail = computed(() => {
  const result = props.mod.primaryThumbnail || (props.mod.thumbnails && props.mod.thumbnails[0]);
  console.log(`🎯 [ThumbnailImage] ${props.mod.fileName} - primaryThumbnail: ${result ? 'Yes' : 'No'}`);
  return result;
});

const placeholderIcon = computed(() => {
  const fileName = props.mod.fileName.toLowerCase();
  
  if (fileName.includes('hair') || fileName.includes('clothing') || fileName.includes('makeup')) {
    return UserIcon;
  }
  if (fileName.includes('furniture') || fileName.includes('decoration') || fileName.includes('build')) {
    return HomeIcon;
  }
  if (fileName.includes('script') || fileName.includes('mod')) {
    return CogIcon;
  }
  if (fileName.includes('gameplay') || fileName.includes('trait')) {
    return PuzzlePieceIcon;
  }
  
  return DocumentIcon;
});

const placeholderText = computed(() => {
  const fileName = props.mod.fileName.toLowerCase();
  
  if (fileName.includes('hair')) return 'Hair';
  if (fileName.includes('clothing')) return 'Clothing';
  if (fileName.includes('makeup')) return 'Makeup';
  if (fileName.includes('furniture')) return 'Furniture';
  if (fileName.includes('decoration')) return 'Decor';
  if (fileName.includes('script')) return 'Script';
  if (fileName.includes('gameplay')) return 'Gameplay';
  if (fileName.includes('trait')) return 'Trait';
  
  return 'Mod';
});

// Methods
const handleLoad = () => {
  loading.value = false;
  emit('load');
};

const handleError = () => {
  loading.value = false;
  emit('error', props.mod.id);
};
</script>

<style scoped>
/* Clean Thumbnail Container - Image-focused design */
.thumbnail-container {
  @apply relative overflow-hidden;
  background: var(--bg-secondary);
  position: relative;
  isolation: isolate;
}

.thumbnail-loading {
  @apply w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700;
}

.loading-spinner {
  @apply w-6 h-6 border-2 border-gray-300 border-t-green-500 rounded-full animate-spin;
}

.thumbnail-image {
  @apply w-full h-full object-cover transition-all duration-300 ease-out;
  position: relative;
  z-index: 1;
  object-position: center center; /* Center the image for optimal cropping */
  /* Ensure image fills square container without distortion */
  object-fit: cover;
}

.thumbnail-placeholder {
  @apply w-full h-full flex flex-col items-center justify-center
         text-gray-500 dark:text-gray-400 transition-all duration-500;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.95) 0%,
    rgba(241, 245, 249, 1) 50%,
    rgba(226, 232, 240, 0.95) 100%);
  backdrop-filter: blur(20px);
  position: relative;
  z-index: 2;
}

.thumbnail-placeholder::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle,
    rgba(45, 159, 43, 0.03) 0%,
    transparent 70%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.placeholder-icon {
  @apply w-8 h-8 mb-2;
}

.placeholder-text {
  @apply text-sm font-medium;
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .thumbnail-placeholder {
    background: linear-gradient(135deg,
      rgba(38, 38, 38, 0.8) 0%,
      rgba(45, 45, 45, 0.9) 50%,
      rgba(38, 38, 38, 0.8) 100%);
  }
}
</style>
