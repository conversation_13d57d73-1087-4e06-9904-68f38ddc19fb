<template>
  <!-- Enhanced CAS Content Section -->
  <div v-if="hasCASContent" class="mod-card__section">
    <h4 class="mod-card__section-title">
      <UserIcon class="mod-card__section-icon" />
      Create-a-Sim Content
    </h4>

    <!-- Universal Subcategory Display -->
    <UniversalSubcategoryDisplay
      v-if="universalClassificationData"
      :classification-result="universalClassificationData"
    />

    <!-- Fallback: Hair Classification Display (backward compatibility) -->
    <HairClassificationDisplay
      v-else-if="hairClassificationData"
      :hair-details="hairClassificationData"
    />

    <!-- General CAS Content Info -->
    <div v-else class="cas-content-info">
      <div class="cas-content-summary">
        <span class="cas-content-type">{{ getCASContentType() }}</span>
        <span class="cas-content-description">{{ getCASContentDescription() }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { UserIcon } from '@heroicons/vue/24/outline';
import UniversalSubcategoryDisplay from '../UniversalSubcategoryDisplay.vue';
import HairClassificationDisplay from '../HairClassificationDisplay.vue';

// Types
interface CASContentItem {
  category: string;
  subcategory: string;
  description: string;
  isHair: boolean;
  isClothing: boolean;
  isMakeup: boolean;
  isAccessory: boolean;
  hairDetails?: {
    length: string;
    style: string[];
    texture: string;
    hasAccessories: boolean;
    confidence: number;
    detectionMethod: string;
    keywords: string[];
  };
  tags: string[];
}

interface UniversalClassification {
  category: string;
  subcategories: string[];
  confidence: number;
  detectionMethod: string;
  keywords: string[];
  metadata: Record<string, any>;
  tags: string[];
}

interface ModData {
  fileName: string;
  fileExtension: string;
  author?: string;
  actualModName?: string;
  actualDescription?: string;
  extractedItemNames?: string[];
  hasStringTable?: boolean;

  // Enhanced CAS Content Analysis Fields
  casContent?: {
    totalItems: number;
    items: CASContentItem[];
  };

  // Universal classification
  universalClassification?: UniversalClassification;

  // Object classification
  objectClassification?: {
    category: string;
    subcategory: string;
    confidence: number;
    detectionMethod: string;
    keywords: string[];
    metadata: any;
    tags: string[];
  };

  // Enhanced Object Content Analysis Fields
  objectContent?: {
    totalItems: number;
    items: Array<{
      category: string;
      subcategory: string;
      roomAssignment: string;
      function: string;
      style: string;
      price: number;
      isDecor: boolean;
      isFunctional: boolean;
      tags: string[];
      description: string;
    }>;
  };
}

// Props
interface Props {
  modData: ModData;
}

const props = defineProps<Props>();

// Computed Properties
const hasObjectContent = computed(() => {
  // Check if we have object content from content analysis
  if (props.modData?.objectContent?.totalItems > 0) {
    return true;
  }

  // Check if we have object classification data
  if (props.modData?.objectClassification) {
    return true;
  }

  return false;
});

const hasCASContent = computed(() => {
  // Don't show CAS content if we have object content (object content takes priority)
  if (hasObjectContent.value) {
    return false;
  }

  // First check if we have enhanced CAS classification data
  if (props.modData?.universalClassification) {
    return true;
  }

  // Check if we have CAS content from content analysis
  if (props.modData?.casContent?.totalItems > 0) {
    return true;
  }

  // Fallback: Check filename for CAS-related keywords
  const fileName = (props.modData?.fileName || '').toLowerCase();
  const casKeywords = ['hair', 'clothing', 'outfit', 'makeup', 'skin', 'accessory', 'cas'];
  
  return casKeywords.some(keyword => fileName.includes(keyword));
});

const universalClassificationData = computed(() => {
  // Get universal classification data for CAS content
  const fileName = (props.modData?.fileName || '').toLowerCase();

  // Check if we have enhanced universal classification data
  if (props.modData?.universalClassification) {
    return props.modData.universalClassification;
  }

  // Generate classification data from available information
  if (hasCASContent.value) {
    const casContent = props.modData?.casContent?.items?.[0];
    if (casContent) {
      return {
        category: casContent.category || 'unknown',
        subcategories: [casContent.subcategory || 'unknown'],
        confidence: 0.8,
        detectionMethod: 'filename',
        keywords: fileName.split(/[\s_-]+/).filter(k => k.length > 2),
        metadata: {},
        tags: casContent.tags || []
      };
    }
  }

  return null;
});

const hairClassificationData = computed(() => {
  // Get hair classification data for backward compatibility
  const casContent = props.modData?.casContent?.items?.find(item => item.isHair);
  
  if (casContent?.hairDetails) {
    return casContent.hairDetails;
  }

  return null;
});

// Methods
const getCASContentType = (): string => {
  const fileName = (props.modData?.fileName || '').toLowerCase();

  if (fileName.includes('hair')) return 'Hair';
  if (fileName.includes('clothing') || fileName.includes('outfit')) return 'Clothing';
  if (fileName.includes('makeup')) return 'Makeup';
  if (fileName.includes('skin')) return 'Skin Details';
  if (fileName.includes('accessory')) return 'Accessories';

  return 'Create-a-Sim Content';
};

const getCASContentDescription = (): string => {
  const fileName = (props.modData?.fileName || '').toLowerCase();

  if (fileName.includes('hair')) {
    return 'Adds new hairstyles for your Sims in Create-a-Sim';
  }
  if (fileName.includes('clothing')) {
    return 'Adds new clothing options for your Sims';
  }
  if (fileName.includes('makeup')) {
    return 'Adds new makeup options for your Sims';
  }
  if (fileName.includes('skin')) {
    return 'Adds new skin details and overlays';
  }

  return 'Enhances Create-a-Sim customization options';
};
</script>

<style scoped>
/* CAS Content Section */
.mod-card__section {
  margin-top: var(--space-6);
  padding: var(--space-5) var(--space-6);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
}

.mod-card__section-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
}

.mod-card__section-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--sims-blue);
}

/* CAS Content Info */
.cas-content-info {
  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--bg-secondary) 100%);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-top: var(--space-3);
}

.cas-content-summary {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.cas-content-type {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--sims-blue);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.cas-content-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

/* Enhanced CAS Content Styles */
.cas-content-info {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid var(--sims-blue-light);
  border-radius: var(--radius-lg);
  padding: 1rem;
}
</style>
