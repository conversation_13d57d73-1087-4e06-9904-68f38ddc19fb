<template>
  <!-- List View Content (shown outside thumbnail for list layout) -->
  <div v-if="viewMode === 'list'" class="list-content">
    <div class="list-header">
      <h3 class="list-title" :title="mod.fileName">{{ displayName }}</h3>
      <div class="list-badges">
        <span v-if="mod.hasConflicts" class="list-badge badge-warning" title="Has conflicts">
          <ExclamationTriangleIcon class="w-3 h-3" />
        </span>
        <span v-if="mod.isCorrupted" class="list-badge badge-error" title="Corrupted file">
          <XCircleIcon class="w-3 h-3" />
        </span>
        <span v-if="isHighQuality" class="list-badge badge-success" title="High quality">
          <SparklesIcon class="w-3 h-3" />
        </span>
      </div>
    </div>
    <div class="list-meta">
      <span class="list-category-badge" :class="getCategoryClass()">
        {{ mod.category || 'Unknown' }}
      </span>
      <span class="list-file-size">{{ formatFileSize(mod.fileSize) }}</span>
      <span v-if="hasMultipleVariations" class="list-variation-indicator">
        +{{ variationCount }} swatches
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  ExclamationTriangleIcon,
  XCircleIcon,
  SparklesIcon
} from '@heroicons/vue/24/outline';

import type { ModData } from '../../../../types/ModData';

// Props
interface Props {
  mod: ModData;
  viewMode: 'grid' | 'list';
}

const props = defineProps<Props>();

// Computed properties
const displayName = computed(() => {
  const name = props.mod.fileName;
  // Remove file extension and clean up the name
  return name.replace(/\.(package|ts4script)$/i, '').replace(/_/g, ' ');
});

const isHighQuality = computed(() => {
  // Determine if this is a high-quality mod based on various factors
  const hasHighResources = (props.mod.resourceCount || 0) > 50;
  const hasLargeSize = (props.mod.fileSize || 0) > 1024 * 1024; // > 1MB
  const hasEnhancedAnalysis = props.mod.objectClassification?.confidence > 0.8;
  
  return hasHighResources || hasLargeSize || hasEnhancedAnalysis;
});

const thumbnailVariations = computed(() => {
  return props.mod.thumbnailVariations || [];
});

const hasMultipleVariations = computed(() => {
  return props.mod.hasMultipleVariations || thumbnailVariations.value.length > 0;
});

const variationCount = computed(() => {
  return thumbnailVariations.value.length;
});

// Methods
const getCategoryClass = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas') || category.includes('create-a-sim')) return 'category-cas';
  if (category.includes('build') || category.includes('buy')) return 'category-build';
  if (category.includes('script')) return 'category-script';
  if (category.includes('gameplay')) return 'category-gameplay';
  
  return 'category-other';
};

const formatFileSize = (bytes: number | undefined): string => {
  if (!bytes) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};
</script>

<style scoped>
/* List View Content */
.list-content {
  @apply flex-1 min-w-0 ml-3;
}

.list-header {
  @apply flex items-start justify-between mb-1;
}

.list-title {
  @apply text-sm font-semibold text-gray-900 dark:text-white leading-tight;
  /* Advanced text overflow handling */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
  margin-right: 0.5rem;
}

.list-badges {
  @apply flex space-x-1 flex-shrink-0;
}

.list-badge {
  @apply flex items-center px-1.5 py-0.5 rounded text-xs font-medium;
}

.list-badge.badge-warning {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

.list-badge.badge-error {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

.list-badge.badge-success {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.list-meta {
  @apply flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400;
}

.list-category-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.list-category-badge.category-cas {
  @apply bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200;
}

.list-category-badge.category-build {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
}

.list-category-badge.category-script {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.list-category-badge.category-gameplay {
  @apply bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200;
}

.list-category-badge.category-other {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200;
}

.list-file-size {
  @apply font-medium;
}

.list-variation-indicator {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 
         px-2 py-1 rounded-full font-medium;
}
</style>
