<template>
  <div class="view-controls">
    <!-- View Mode Toggle -->
    <div class="view-mode-toggle">
      <button
        :class="['view-mode-btn', { active: viewMode === 'grid' }]"
        @click="handleViewModeChange('grid')"
        title="Grid View"
      >
        <Squares2X2Icon class="w-5 h-5" />
      </button>
      <button
        :class="['view-mode-btn', { active: viewMode === 'list' }]"
        @click="handleViewModeChange('list')"
        title="List View"
      >
        <ListBulletIcon class="w-5 h-5" />
      </button>
    </div>
    
    <!-- Sort Controls -->
    <div class="sort-controls">
      <select 
        :value="sortBy" 
        @change="handleSortByChange"
        class="sort-select"
      >
        <option value="name">Name</option>
        <option value="category">Category</option>
        <option value="size">File Size</option>
        <option value="date">Date Added</option>
      </select>
      <button
        :class="['sort-order-btn', { desc: sortOrder === 'desc' }]"
        @click="handleSortOrderToggle"
        :title="`Sort ${sortOrder === 'asc' ? 'Ascending' : 'Descending'}`"
      >
        <ChevronUpIcon v-if="sortOrder === 'asc'" class="w-4 h-4" />
        <ChevronDownIcon v-else class="w-4 h-4" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Squares2X2Icon,
  ListBulletIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@heroicons/vue/24/outline';

// Props
interface Props {
  viewMode: 'grid' | 'list';
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:viewMode': [mode: 'grid' | 'list'];
  'update:sortBy': [sortBy: string];
  'update:sortOrder': [order: 'asc' | 'desc'];
  viewModeChange: [mode: 'grid' | 'list'];
  sortChange: [sortBy: string, sortOrder: 'asc' | 'desc'];
}>();

// Methods
const handleViewModeChange = (mode: 'grid' | 'list') => {
  emit('update:viewMode', mode);
  emit('viewModeChange', mode);
};

const handleSortByChange = (event: Event) => {
  const target = event.target as HTMLSelectElement;
  const newSortBy = target.value;
  emit('update:sortBy', newSortBy);
  emit('sortChange', newSortBy, props.sortOrder);
};

const handleSortOrderToggle = () => {
  const newOrder = props.sortOrder === 'asc' ? 'desc' : 'asc';
  emit('update:sortOrder', newOrder);
  emit('sortChange', props.sortBy, newOrder);
};
</script>

<style scoped>
.view-controls {
  @apply flex items-center space-x-4;
}

.view-mode-toggle {
  @apply flex rounded-lg border border-gray-300 dark:border-gray-600 overflow-hidden;
}

.view-mode-btn {
  @apply px-3 py-2 bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 
         hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200
         focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50;
}

.view-mode-btn.active {
  @apply bg-green-500 text-white hover:bg-green-600;
}

.sort-controls {
  @apply flex items-center space-x-2;
}

.sort-select {
  @apply px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
         bg-white dark:bg-gray-700 text-gray-900 dark:text-white
         focus:ring-2 focus:ring-green-500 focus:border-transparent
         transition-colors duration-200;
}

.sort-select:focus {
  @apply outline-none;
}

.sort-order-btn {
  @apply p-2 border border-gray-300 dark:border-gray-600 rounded-lg
         bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300
         hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200
         focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50;
}

.sort-order-btn.desc {
  @apply bg-gray-100 dark:bg-gray-600;
}
</style>
