<template>
  <div :class="['mod-thumbnail-card', viewMode, { selected: isSelected }]" @click="handleClick">
    <!-- Thumbnail Section -->
    <div class="thumbnail-container">
      <!-- Thumbnail Image Component -->
      <ThumbnailImage
        :mod="mod"
        :view-mode="viewMode"
        @load="handleThumbnailLoad"
        @error="handleThumbnailError"
      />

      <!-- Hover Text Overlay Component -->
      <ThumbnailOverlay
        :mod="mod"
        :view-mode="viewMode"
      />
    </div>

    <!-- List View Content Component -->
    <ThumbnailListContent
      :mod="mod"
      :view-mode="viewMode"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';


import type { ModData } from '../../../types/ModData';
import type { ThumbnailData } from '../../../services/visual/ThumbnailExtractionService';
import ThumbnailImage from './thumbnail/ThumbnailImage.vue';
import ThumbnailOverlay from './thumbnail/ThumbnailOverlay.vue';
import ThumbnailListContent from './thumbnail/ThumbnailListContent.vue';

// Props
interface Props {
  mod: ModData;
  viewMode: 'grid' | 'list';
  isSelected?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false
});

// Emits
const emit = defineEmits<{
  click: [mod: ModData];
  thumbnailError: [modId: string];
}>();

// Reactive state
const thumbnailLoading = ref(false);

// Computed thumbnail URL from enhanced data
const thumbnailUrl = computed(() => {
  // Use enhanced thumbnail data if available (imageData is the correct property)
  if (primaryThumbnail.value?.imageData) {
    console.log(`🎯 [ModThumbnailCard] ${props.mod.fileName} - Using primaryThumbnail.imageData`);
    return primaryThumbnail.value.imageData;
  }
  // Fallback to legacy thumbnail data
  if (props.mod.thumbnailData) {
    console.log(`🎯 [ModThumbnailCard] ${props.mod.fileName} - Using mod.thumbnailData`);
    return props.mod.thumbnailData;
  }
  if (props.mod.thumbnailUrl) {
    console.log(`🎯 [ModThumbnailCard] ${props.mod.fileName} - Using mod.thumbnailUrl`);
    return props.mod.thumbnailUrl;
  }
  console.log(`❌ [ModThumbnailCard] ${props.mod.fileName} - No thumbnail data found`);
  return null;
});


// Computed properties



// Enhanced thumbnail support
const hasThumbnails = computed(() => {
  const result = props.mod.thumbnails && props.mod.thumbnails.length > 0;
  console.log(`🔍 [ModThumbnailCard] ${props.mod.fileName} - hasThumbnails: ${result}, thumbnails: ${props.mod.thumbnails?.length || 0}`);
  return result;
});

const primaryThumbnail = computed(() => {
  const result = props.mod.primaryThumbnail || (props.mod.thumbnails && props.mod.thumbnails[0]);
  console.log(`🎯 [ModThumbnailCard] ${props.mod.fileName} - primaryThumbnail: ${result ? 'Yes' : 'No'}`);
  return result;
});



// Methods
const handleClick = () => {
  emit('click', props.mod);
};

const handleThumbnailLoad = () => {
  thumbnailLoading.value = false;
};

const handleThumbnailError = () => {
  thumbnailLoading.value = false;
  // thumbnailUrl is now computed, can't set it directly
  emit('thumbnailError', props.mod.id);
};













// Lifecycle
onMounted(() => {
  console.log(`🚀 [ModThumbnailCard] Mounted: ${props.mod.fileName}`);
});


</script>

<style scoped>
/* Import shared design system styles */
@import '../../styles/dashboard-shared.css';
@import '../../styles/simonitor-design-system.css';

/* Professional Card Design - Apple-inspired with Sims 4 theming */
.mod-thumbnail-card {
  @apply cursor-pointer overflow-hidden transition-all duration-300 ease-out relative;

  /* Clean, image-focused design with strategic color system */
  background: var(--bg-elevated);
  border-radius: 10px; /* Perfect square with rounded corners */
  border: 1px solid var(--border-light);

  /* Subtle shadow for depth without distraction */
  box-shadow: var(--shadow-sm);

  /* Performance optimizations */
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform, box-shadow, border-color;

  /* Enhanced visual hierarchy */
  position: relative;
  isolation: isolate;
}

.mod-thumbnail-card:hover {
  /* Clean, subtle hover animation focusing on the image */
  transform: translateY(-4px) scale(1.02) translateZ(0);
  border-color: var(--color-primary);

  /* Enhanced depth with new color system */
  box-shadow: var(--shadow-lg);

  /* Clean background enhancement */
  background: var(--bg-elevated);
}

.mod-thumbnail-card:active {
  /* Refined press feedback */
  transform: translateY(-4px) scale(1.01) translateZ(0);
  transition-duration: 150ms;
  box-shadow: var(--shadow-md);
}

.mod-thumbnail-card.selected {
  /* Premium selection state with strategic colors */
  border: 2px solid var(--primary);
  transform: translateY(-6px) scale(1.03) translateZ(0);

  /* Sophisticated selection glow with strategic colors */
  box-shadow: var(--shadow-xl);

  /* Subtle branded background */
  background: linear-gradient(145deg,
    var(--primary-bg) 0%,
    var(--bg-elevated) 50%,
    var(--primary-bg) 100%);
}

.mod-thumbnail-card.selected:hover {
  /* Enhanced selection hover with premium feel */
  transform: translateY(-8px) scale(1.04) translateZ(0);
  box-shadow: var(--shadow-2xl);
}

.mod-thumbnail-card.list {
  @apply flex items-center space-x-4;
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  height: auto;
  aspect-ratio: unset;
}

/* List view - show content outside overlay */
.mod-thumbnail-card.list .hover-text-overlay {
  display: none; /* Hide overlay in list view */
}



.mod-thumbnail-card.grid {
  /* Perfect square aspect ratio for clean, image-focused design */
  aspect-ratio: 1;
  width: 100%;
  position: relative;
}

/* Clean Thumbnail Container - Image-focused design */
.thumbnail-container {
  @apply relative overflow-hidden;
  background: var(--bg-secondary);
  position: relative;
  isolation: isolate;
}

.mod-thumbnail-card.grid .thumbnail-container {
  /* Perfect square aspect ratio - fills entire card */
  aspect-ratio: 1;
  width: 100%;
  height: 100%;
  border-radius: 10px; /* Match card border radius completely */
  overflow: hidden;
}

.mod-thumbnail-card.list .thumbnail-container {
  @apply w-20 h-20 flex-shrink-0;
  border-radius: 8px; /* Slightly smaller radius for list view */
  box-shadow: var(--shadow-subtle);
  aspect-ratio: 1; /* Ensure square even in list view */
}







/* Grid cards are now purely image-focused - no separate content section needed */

/* Old content styles removed - content now shown in hover overlay */

/* Professional badges with strategic color system - Overlay optimized */
.category-badge {
  @apply px-2 py-1 rounded-full text-xs font-bold;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);

  /* Smooth transitions with professional easing */
  transition: all 0.3s ease-out;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4), 0 1px 3px rgba(0, 0, 0, 0.6);
}

.category-cas {
  /* Strategic purple for CAS content */
  background: var(--premium);
}

.category-build {
  /* Strategic blue for Build/Buy content */
  background: var(--primary);
}

.category-script {
  /* Strategic green for script content */
  background: var(--success);
}

.category-gameplay {
  /* Strategic orange for gameplay content */
  background: var(--warning);
}

.category-other {
  background: var(--secondary);
}

.file-size {
  @apply text-xs font-medium;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}

/* List View Details */
.mod-details {
  @apply mt-2;
}

.mod-description {
  @apply text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2;
}

.mod-stats {
  @apply flex space-x-4 text-xs text-gray-500 dark:text-gray-400;
}

.stat-item {
  @apply flex space-x-1;
}

.stat-label {
  @apply font-medium;
}





/* Professional Focus States for Accessibility */
.mod-thumbnail-card:focus-visible {
  outline: none;
  border-color: #2D9F2B;
  box-shadow:
    0 0 0 3px rgba(45, 159, 43, 0.2),
    0 8px 25px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px) scale(1.01);
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .mod-thumbnail-card {
    background: #1a1a1a;
    border-color: rgba(255, 255, 255, 0.08);
  }

  .mod-title {
    color: #ffffff;
  }

  .mod-thumbnail-card:hover .mod-title {
    color: #22C55E;
  }

  /* Dark mode content section removed - using hover overlay only */

  .thumbnail-placeholder {
    background: linear-gradient(135deg,
      rgba(38, 38, 38, 0.8) 0%,
      rgba(45, 45, 45, 0.9) 50%,
      rgba(38, 38, 38, 0.8) 100%);
  }
}
</style>
