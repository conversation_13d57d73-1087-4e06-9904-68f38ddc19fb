<template>
  <div class="mod-display-area">
    <!-- Loading State -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>Loading thumbnails...</p>
    </div>
    
    <!-- Empty State -->
    <div v-else-if="mods.length === 0" class="empty-state">
      <FolderOpenIcon class="empty-icon" />
      <h3>No mods found</h3>
      <p>Try adjusting your search or category filters</p>
    </div>
    
    <!-- Mod Grid/List -->
    <div v-else :class="['mod-grid', viewMode]">
      <ModThumbnailCard
        v-for="mod in mods"
        :key="mod.id"
        :mod="mod"
        :view-mode="viewMode"
        @click="handleModSelect(mod)"
        @thumbnail-error="handleThumbnailError"
      />
    </div>
    
    <!-- Pagination -->
    <div v-if="showPagination" class="pagination">
      <button
        :disabled="currentPage === 1"
        @click="handlePageChange(currentPage - 1)"
        class="pagination-btn"
        aria-label="Previous page"
      >
        <ChevronLeftIcon class="w-4 h-4" />
        Previous
      </button>
      
      <div class="page-numbers">
        <button
          v-for="page in visiblePages"
          :key="page"
          :class="['page-btn', { active: page === currentPage }]"
          @click="handlePageChange(page)"
          :aria-label="`Go to page ${page}`"
          :aria-current="page === currentPage ? 'page' : undefined"
        >
          {{ page }}
        </button>
      </div>
      
      <button
        :disabled="currentPage === totalPages"
        @click="handlePageChange(currentPage + 1)"
        class="pagination-btn"
        aria-label="Next page"
      >
        Next
        <ChevronRightIcon class="w-4 h-4" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ChevronLeftIcon, ChevronRightIcon, FolderOpenIcon } from '@heroicons/vue/24/outline';
import ModThumbnailCard from '../ModThumbnailCard.vue';
import type { ModData } from '../../../../types/ModData';

// Props
interface Props {
  mods: ModData[];
  viewMode: 'grid' | 'list';
  loading?: boolean;
  currentPage: number;
  totalPages: number;
  itemsPerPage?: number;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  itemsPerPage: 24
});

// Emits
const emit = defineEmits<{
  modSelect: [mod: ModData];
  pageChange: [page: number];
  thumbnailError: [modId: string];
}>();

// Computed properties
const showPagination = computed(() => {
  return props.totalPages > 1;
});

const visiblePages = computed(() => {
  const pages = [];
  const total = props.totalPages;
  const current = props.currentPage;
  
  // Show up to 7 pages around current page
  let start = Math.max(1, current - 3);
  let end = Math.min(total, current + 3);
  
  // Adjust if we're near the beginning or end
  if (end - start < 6) {
    if (start === 1) {
      end = Math.min(total, start + 6);
    } else {
      start = Math.max(1, end - 6);
    }
  }
  
  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  
  return pages;
});

// Methods
const handleModSelect = (mod: ModData) => {
  emit('modSelect', mod);
};

const handlePageChange = (page: number) => {
  if (page >= 1 && page <= props.totalPages) {
    emit('pageChange', page);
  }
};

const handleThumbnailError = (modId: string) => {
  emit('thumbnailError', modId);
};
</script>

<style scoped>
.mod-display-area {
  @apply flex-1 flex flex-col overflow-hidden;
}

.loading-state, .empty-state {
  @apply flex-1 flex flex-col items-center justify-center text-gray-500 dark:text-gray-400;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-gray-300 border-t-green-500 rounded-full animate-spin mb-4;
}

.empty-icon {
  @apply w-16 h-16 mb-4;
}

.empty-state h3 {
  @apply text-lg font-semibold mb-2;
}

.empty-state p {
  @apply text-sm;
}

.mod-grid {
  @apply flex-1 overflow-auto p-4;
}

.mod-grid.grid {
  @apply grid gap-4;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.mod-grid.list {
  @apply space-y-2;
}

/* Pagination */
.pagination {
  @apply flex items-center justify-center space-x-2 p-4 border-t border-gray-200 dark:border-gray-700;
}

.pagination-btn {
  @apply flex items-center space-x-1 px-3 py-2 border border-gray-300 dark:border-gray-600 
         rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300
         hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed
         transition-colors duration-200
         focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50;
}

.page-numbers {
  @apply flex space-x-1;
}

.page-btn {
  @apply px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
         bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300
         hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200
         focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50;
}

.page-btn.active {
  @apply bg-green-500 text-white border-green-500 hover:bg-green-600;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    @apply animate-none;
  }
  
  .pagination-btn,
  .page-btn {
    @apply transition-none;
  }
}
</style>
