<template>
  <div class="mod-card-actions">
    <!-- Installation & Compatibility -->
    <div class="mod-card__section">
      <h4 class="mod-card__section-title">
        <CogIcon class="mod-card__section-icon" />
        Installation & Compatibility
      </h4>
      <div class="mod-card__installation-info">
        <div class="installation-note">
          <strong>Installation:</strong> {{ getInstallationNotes() }}
        </div>
        <div v-if="isScript" class="compatibility-warning">
          <strong>⚠️ Script Mod:</strong> Enable script mods in Game Options > Other > Script Mods Allowed
        </div>
        <div v-if="expansionRequirements.length > 0" class="expansion-requirements">
          <strong>Requires:</strong> {{ expansionRequirements.join(', ') }}
        </div>
        <div class="compatibility-info">
          <strong>Compatibility:</strong> {{ getGameCompatibility() }}
        </div>
        <div class="file-info">
          <strong>File Type:</strong> {{ getFileTypeDescription() }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { CogIcon } from '@heroicons/vue/24/outline';

// Types
interface ModData {
  fileName: string;
  fileExtension: string;
  fileSize: number;
  author?: string;
  version?: string;
  metadataConfidence?: number;
}

// Props
interface Props {
  modData: ModData;
}

const props = defineProps<Props>();

// Computed properties
const isScript = computed(() => (props.modData?.fileExtension || '.package') === '.ts4script');

const expansionRequirements = computed(() => {
  const fileName = (props.modData?.fileName || '').toLowerCase();
  const requirements: string[] = [];

  // Detect expansion pack requirements from filename
  if (fileName.includes('gtw') || fileName.includes('gettowork')) requirements.push('Get to Work');
  if (fileName.includes('gts') || fileName.includes('gettogether')) requirements.push('Get Together');
  if (fileName.includes('cl') || fileName.includes('cityliving')) requirements.push('City Living');
  if (fileName.includes('cats') || fileName.includes('catsdogs')) requirements.push('Cats & Dogs');
  if (fileName.includes('seasons')) requirements.push('Seasons');
  if (fileName.includes('fame') || fileName.includes('getfamous')) requirements.push('Get Famous');
  if (fileName.includes('island') || fileName.includes('islandliving')) requirements.push('Island Living');
  if (fileName.includes('magic') || fileName.includes('realmofmagic')) requirements.push('Realm of Magic');
  if (fileName.includes('discover') || fileName.includes('discoveruniversity')) requirements.push('Discover University');
  if (fileName.includes('eco') || fileName.includes('ecolifestyle')) requirements.push('Eco Lifestyle');
  if (fileName.includes('snowy') || fileName.includes('snowyescape')) requirements.push('Snowy Escape');
  if (fileName.includes('cottage') || fileName.includes('cottageliving')) requirements.push('Cottage Living');
  if (fileName.includes('werewolves')) requirements.push('Werewolves');
  if (fileName.includes('highschool')) requirements.push('High School Years');
  if (fileName.includes('growing') || fileName.includes('growingtogether')) requirements.push('Growing Together');

  return requirements;
});

// Methods
const getInstallationNotes = (): string => {
  if (isScript.value) {
    return 'Requires script mods to be enabled in game options';
  }

  return 'Place in Mods folder';
};

const getGameCompatibility = (): string => {
  if (isScript.value) {
    return 'Requires latest game version for script compatibility';
  }

  return 'Compatible with all game versions';
};

const getFileTypeDescription = (): string => {
  const extension = props.modData?.fileExtension || '.package';
  
  switch (extension.toLowerCase()) {
    case '.package':
      return 'Standard Sims 4 mod package - Contains custom content or gameplay modifications';
    case '.ts4script':
      return 'Script mod - Requires script mods to be enabled in game settings';
    default:
      return `${extension.toUpperCase()} file - Custom mod format`;
  }
};
</script>

<style scoped>
.mod-card-actions {
  display: flex;
  flex-direction: column;
}

.mod-card__section {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
}

.mod-card__section:last-child {
  border-bottom: none;
}

.mod-card__section-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
}

.mod-card__section-icon {
  width: 20px;
  height: 20px;
  color: var(--sims-blue);
  flex-shrink: 0;
}

/* Installation Info Styles */
.mod-card__installation-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.installation-note,
.compatibility-info,
.file-info {
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
}

.installation-note strong,
.compatibility-info strong,
.file-info strong {
  color: var(--text-primary);
  font-weight: var(--font-semibold);
}

.compatibility-warning {
  padding: var(--space-3);
  background-color: var(--warning-bg);
  border: 1px solid var(--warning-border);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  color: var(--warning-text);
}

.compatibility-warning strong {
  color: var(--warning-text);
  font-weight: var(--font-semibold);
}

.expansion-requirements {
  padding: var(--space-3);
  background-color: var(--info-bg);
  border: 1px solid var(--info-border);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  color: var(--info-text);
}

.expansion-requirements strong {
  color: var(--info-text);
  font-weight: var(--font-semibold);
}
</style>
