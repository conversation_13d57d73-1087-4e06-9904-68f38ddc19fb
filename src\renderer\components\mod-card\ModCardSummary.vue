<template>
  <div class="mod-card__section mod-card__summary">
    <h4 class="mod-card__section-title">
      <StarIcon class="mod-card__section-icon" />
      What This Mod Adds
    </h4>
    <div class="mod-summary">
      <p class="mod-summary__description">{{ getModDescription() }}</p>
      <div class="mod-summary__tags">
        <span class="mod-tag mod-tag--category" :class="`mod-tag--${getModCategory()}`">
          <component :is="getCategoryIcon()" class="mod-tag__icon" />
          {{ getModCategoryLabel() }}
        </span>
        <span class="mod-tag mod-tag--content">
          {{ getContentType() }}
        </span>
        <span v-if="modData?.author" class="mod-tag mod-tag--author">
          by {{ modData.author }}
        </span>
        <span v-if="(modData?.fileExtension || '.package') === '.ts4script'" class="mod-tag mod-tag--script">
          Script Mod
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { StarIcon, UserIcon, HomeIcon, PuzzlePieceIcon, GlobeAltIcon, CommandLineIcon, CogIcon } from '@heroicons/vue/24/outline';

// Types
interface ModData {
  fileName: string;
  fileExtension: string;
  author?: string;
  actualModName?: string;
  actualDescription?: string;
  
  // Enhanced CAS Content Analysis Fields
  casContent?: {
    totalItems: number;
    items: Array<{
      category: string;
      subcategory: string;
      description: string;
      isHair: boolean;
      isClothing: boolean;
      isMakeup: boolean;
      isAccessory: boolean;
      tags: string[];
    }>;
  };

  // Enhanced Object Content Analysis Fields
  objectContent?: {
    totalItems: number;
    items: Array<{
      category: string;
      subcategory: string;
      roomAssignment: string;
      function: string;
      style: string;
      price: number;
      isDecor: boolean;
      isFunctional: boolean;
      tags: string[];
      description: string;
    }>;
  };

  // Enhanced Classification Data for UI Display
  objectClassification?: {
    category: string;
    subcategories: string[];
    specificType: string;
    confidence: number;
    detectionMethod: string;
    roomTypes: string[];
    functionality: string[];
    tags: string[];
    metadata: {
      priceRange?: string;
      description?: string;
    };
  };

  universalClassification?: {
    category: string;
    subcategories: string[];
    confidence: number;
    detectionMethod: string;
    keywords: string[];
    metadata: any;
    tags: string[];
  };
}

// Props
interface Props {
  modData: ModData;
}

const props = defineProps<Props>();

// Methods
const getModDescription = (): string => {
  // Use actual description from StringTable if available
  if (props.modData?.actualDescription) {
    return props.modData.actualDescription;
  }

  // NEW: Use extracted mod name and author to create a meaningful description
  if (props.modData?.actualModName || props.modData?.author) {
    const modName = props.modData.actualModName || 'Custom Content';
    const author = props.modData.author;

    if (author && modName) {
      return `${modName} by ${author} - Custom content for The Sims 4.`;
    } else if (modName) {
      return `${modName} - Custom content for The Sims 4.`;
    } else if (author) {
      return `Custom content by ${author} for The Sims 4.`;
    }
  }

  // ENHANCED: Use enhanced categorization data as fallback
  if (props.modData?.objectClassification) {
    const objClass = props.modData.objectClassification;
    const category = objClass.category.toLowerCase();
    const specificType = objClass.specificType.toLowerCase();

    if (category === 'furniture') {
      if (specificType.includes('chair') || specificType.includes('seating')) {
        return 'Adds new seating furniture for your Sims\' homes and community lots.';
      } else if (specificType.includes('table') || specificType.includes('surface')) {
        return 'Adds new tables and surfaces for dining, working, and decorating.';
      } else if (specificType.includes('bed')) {
        return 'Adds new beds and sleeping furniture for your Sims\' bedrooms.';
      } else if (specificType.includes('storage') || specificType.includes('cabinet') || specificType.includes('shelf')) {
        return 'Adds new storage furniture and organizational solutions for your Sims\' homes.';
      } else if (specificType.includes('lighting') || specificType.includes('lamp')) {
        return 'Adds new lighting fixtures and lamps to illuminate your Sims\' spaces.';
      } else {
        return `Adds new ${specificType} furniture for Build/Buy mode.`;
      }
    } else if (category === 'appliances') {
      return 'Adds new appliances and functional objects for your Sims\' homes.';
    } else if (category === 'decoration') {
      return 'Adds new decorative objects and accessories for styling your Sims\' spaces.';
    } else if (category === 'electronics') {
      return 'Adds new electronic devices and entertainment systems for your Sims.';
    } else if (category === 'plumbing') {
      return 'Adds new plumbing fixtures and bathroom essentials for your Sims\' homes.';
    } else {
      return `Adds new ${category} content for Build/Buy mode.`;
    }
  }

  // Check for enhanced CAS classification
  if (props.modData?.universalClassification) {
    const casClass = props.modData.universalClassification;
    const category = casClass.category.toLowerCase();

    if (category.includes('hair')) {
      return 'Adds new hairstyles for your Sims in Create-a-Sim.';
    } else if (category.includes('clothing')) {
      return 'Adds new clothing and outfit options for your Sims.';
    } else if (category.includes('makeup')) {
      return 'Adds new makeup and facial details for Create-a-Sim.';
    } else if (category.includes('accessory')) {
      return 'Adds new accessories and jewelry for your Sims.';
    } else {
      return `Adds new ${category} content for Create-a-Sim.`;
    }
  }

  // Fallback to filename-based categorization
  const fileName = (props.modData?.fileName || '').toLowerCase();
  const isScript = (props.modData?.fileExtension || '.package') === '.ts4script';

  // More detailed analysis for specific content types
  if (fileName.includes('hair')) {
    if (fileName.includes('male')) return 'Adds new hairstyles for male Sims in Create-a-Sim.';
    if (fileName.includes('female')) return 'Adds new hairstyles for female Sims in Create-a-Sim.';
    if (fileName.includes('child')) return 'Adds new hairstyles for child Sims in Create-a-Sim.';
    return 'Adds new hairstyles for your Sims in Create-a-Sim.';
  }

  if (fileName.includes('clothing') || fileName.includes('outfit')) {
    if (fileName.includes('formal')) return 'Adds new formal clothing options for special occasions.';
    if (fileName.includes('casual')) return 'Adds new casual clothing for everyday wear.';
    if (fileName.includes('athletic')) return 'Adds new athletic wear for active Sims.';
    return 'Adds new clothing and outfit options for your Sims.';
  }

  if (fileName.includes('makeup')) {
    return 'Adds new makeup and facial details for Create-a-Sim.';
  }

  if (fileName.includes('skin')) {
    return 'Adds new skin details and overlays for your Sims.';
  }

  if (fileName.includes('furniture')) {
    return 'Adds new furniture pieces for your Sims\' homes.';
  }

  if (fileName.includes('decoration')) {
    return 'Adds new decorative items to enhance your builds.';
  }

  if (isScript) {
    return 'Modifies game behavior and adds new functionality through scripting.';
  }

  return 'Custom content for The Sims 4.';
};

const getModCategory = (): string => {
  // ENHANCED: Use enhanced categorization data first
  if (props.modData?.objectClassification) {
    return 'buildbuy';
  }

  if (props.modData?.universalClassification) {
    return 'cas';
  }

  // Check content analysis data
  if (props.modData?.objectContent?.totalItems > 0) {
    return 'buildbuy';
  }

  if (props.modData?.casContent?.totalItems > 0) {
    return 'cas';
  }

  // Fallback to filename-based categorization
  const fileName = (props.modData?.fileName || '').toLowerCase();
  const isScript = (props.modData?.fileExtension || '.package') === '.ts4script';

  if (isScript) return 'script';
  if (fileName.includes('hair') || fileName.includes('clothing') || fileName.includes('makeup') || fileName.includes('skin')) return 'cas';
  if (fileName.includes('furniture') || fileName.includes('object') || fileName.includes('decoration')) return 'buildbuy';
  if (fileName.includes('trait') || fileName.includes('career') || fileName.includes('skill')) return 'gameplay';
  if (fileName.includes('lot') || fileName.includes('world')) return 'world';

  return 'general';
};

const getModCategoryLabel = (): string => {
  const category = getModCategory();

  switch (category) {
    case 'cas': return 'Create-a-Sim';
    case 'buildbuy': return 'Build/Buy';
    case 'gameplay': return 'Gameplay';
    case 'world': return 'World/Lots';
    case 'script': return 'Script Mod';
    default: return 'General';
  }
};

const getContentType = (): string => {
  // ENHANCED: Use enhanced categorization data first
  if (props.modData?.objectClassification) {
    const objClass = props.modData.objectClassification;
    const specificType = objClass.specificType;

    // Return the specific type with proper capitalization
    return specificType.split(' ').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  }

  if (props.modData?.universalClassification) {
    const casClass = props.modData.universalClassification;
    const category = casClass.category;

    // Return the CAS category with proper capitalization
    return category.split(' ').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  }

  // Fallback to filename-based detection
  const fileName = (props.modData?.fileName || '').toLowerCase();

  // Specific content type detection
  if (fileName.includes('hair')) return 'Hair';
  if (fileName.includes('clothing') || fileName.includes('outfit')) return 'Clothing';
  if (fileName.includes('skin')) return 'Skin';
  if (fileName.includes('makeup')) return 'Makeup';
  if (fileName.includes('trait')) return 'Traits';
  if (fileName.includes('career')) return 'Careers';
  if (fileName.includes('skill')) return 'Skills';
  if (fileName.includes('furniture')) return 'Furniture';
  if (fileName.includes('object')) return 'Objects';
  if (fileName.includes('lot')) return 'Lots';
  if (fileName.includes('world')) return 'Worlds';

  return getModCategoryLabel();
};

const getCategoryIcon = () => {
  const category = getModCategory();

  switch (category) {
    case 'cas': return UserIcon;
    case 'buildbuy': return HomeIcon;
    case 'gameplay': return PuzzlePieceIcon;
    case 'world': return GlobeAltIcon;
    case 'script': return CommandLineIcon;
    default: return CogIcon;
  }
};
</script>

<style scoped>
.mod-card__section {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
}

.mod-card__section:last-child {
  border-bottom: none;
}

.mod-card__section-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
  line-height: var(--leading-tight);
}

.mod-card__section-icon {
  width: 20px;
  height: 20px;
  color: var(--sims-blue);
  flex-shrink: 0;
}

/* Summary Section */
.mod-summary {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.mod-summary__description {
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
  margin: 0;
}

.mod-summary__tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.mod-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mod-tag__icon {
  width: 0.875rem;
  height: 0.875rem;
}

.mod-tag--cas {
  background: #e3f2fd;
  color: #1565c0;
}

.mod-tag--buildbuy {
  background: #f3e5f5;
  color: #7b1fa2;
}

.mod-tag--gameplay {
  background: #e8f5e8;
  color: #2e7d32;
}

.mod-tag--world {
  background: #fff3e0;
  color: #ef6c00;
}

.mod-tag--script {
  background: #fce4ec;
  color: #c2185b;
}

.mod-tag--general {
  background: #f5f5f5;
  color: #616161;
}

.mod-tag--content {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
}

.mod-tag--author {
  background: var(--accent-bg);
  color: var(--accent-text);
  border: 1px solid var(--accent-border);
}

.mod-tag--script {
  background: var(--warning-bg);
  color: var(--warning-text);
  border: 1px solid var(--warning-border);
}
</style>
